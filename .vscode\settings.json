{"java.test.config": {"name": "测试配置", "workingDirectory": "${workspaceFolder}", "vmargs": ["-ea", "-Dspring.profiles.active=test", "-Djunit.jupiter.displayname.generator.default=org.junit.jupiter.api.DisplayNameGenerator$ReplaceUnderscores"]}, "java.test.defaultConfig": "测试配置", "java.test.editor.enableShortcuts": true, "java.debug.settings.console": "internalConsole", "java.debug.settings.hotCodeReplace": "auto", "java.configuration.updateBuildConfiguration": "automatic", "java.test.reportNewProblems": true, "java.test.showInExplorer": true, "java.compile.nullAnalysis.mode": "automatic"}