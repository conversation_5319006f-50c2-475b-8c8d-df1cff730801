package com.example.usertest.dto;

/**
 * 用户数据传输对象
 * 用于在不同层之间传递用户数据，不包含敏感信息如密码
 */
public class UserDTO {
    private Long id;
    private String username;
    private String email;
    private String phone;
    private boolean active;

    public UserDTO() {}

    public UserDTO(Long id, String username, String email, boolean active) {
        this.id = id;
        this.username = username;
        this.email = email;
        this.active = active;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }
}