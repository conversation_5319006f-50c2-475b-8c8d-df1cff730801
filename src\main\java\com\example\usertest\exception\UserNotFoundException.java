package com.example.usertest.exception;

/**
 * 用户未找到异常
 * 当查找的用户不存在时抛出
 */
public class UserNotFoundException extends RuntimeException {
    
    public UserNotFoundException(String message) {
        super(message);
    }
    
    public UserNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public UserNotFoundException(Long userId) {
        super("User not found with id: " + userId);
    }
}