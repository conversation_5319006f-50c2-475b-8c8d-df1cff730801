package com.example.usertest.repository;

import com.example.usertest.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * UserRepository的JDBC实现示例
 * 这个类展示了如何使用JDBC来实现Repository接口
 * 注意：在单元测试中，我们使用Mock对象，而不是这个实际实现
 */
// @Repository // Commented out to avoid conflict with MyBatisPlusUserRepository
public class JdbcUserRepository implements UserRepository {
    
    private final JdbcTemplate jdbcTemplate;
    
    @Autowired
    public JdbcUserRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
    
    // RowMapper用于将ResultSet映射到User对象
    private final RowMapper<User> userRowMapper = new RowMapper<User>() {
        @Override
        public User mapRow(ResultSet rs, int rowNum) throws SQLException {
            User user = new User();
            user.setId(rs.getLong("id"));
            user.setUsername(rs.getString("username"));
            user.setPassword(rs.getString("password"));
            user.setEmail(rs.getString("email"));
            user.setPhone(rs.getString("phone"));
            user.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
            user.setUpdatedAt(rs.getTimestamp("updated_at").toLocalDateTime());
            user.setActive(rs.getBoolean("active"));
            return user;
        }
    };
    
    @Override
    public User save(User user) {
        if (user.getId() == null) {
            // 插入新用户
            String sql = "INSERT INTO users (username, password, email, phone, created_at, updated_at, active) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?)";
            jdbcTemplate.update(sql, 
                user.getUsername(), 
                user.getPassword(), 
                user.getEmail(), 
                user.getPhone(),
                user.getCreatedAt(),
                user.getUpdatedAt(),
                user.isActive()
            );
            
            // 获取生成的ID（这里简化处理，实际项目中需要更复杂的处理）
            Long id = jdbcTemplate.queryForObject("SELECT LAST_INSERT_ID()", Long.class);
            user.setId(id);
        } else {
            // 更新现有用户
            String sql = "UPDATE users SET username = ?, password = ?, email = ?, phone = ?, " +
                        "updated_at = ?, active = ? WHERE id = ?";
            jdbcTemplate.update(sql,
                user.getUsername(),
                user.getPassword(),
                user.getEmail(),
                user.getPhone(),
                user.getUpdatedAt(),
                user.isActive(),
                user.getId()
            );
        }
        return user;
    }
    
    @Override
    public Optional<User> findByUsername(String username) {
        String sql = "SELECT * FROM users WHERE username = ?";
        try {
            User user = jdbcTemplate.queryForObject(sql, userRowMapper, username);
            return Optional.ofNullable(user);
        } catch (Exception e) {
            return Optional.empty();
        }
    }
    
    @Override
    public Optional<User> findByEmail(String email) {
        String sql = "SELECT * FROM users WHERE email = ?";
        try {
            User user = jdbcTemplate.queryForObject(sql, userRowMapper, email);
            return Optional.ofNullable(user);
        } catch (Exception e) {
            return Optional.empty();
        }
    }
    
    @Override
    public Optional<User> findById(Long id) {
        String sql = "SELECT * FROM users WHERE id = ?";
        try {
            User user = jdbcTemplate.queryForObject(sql, userRowMapper, id);
            return Optional.ofNullable(user);
        } catch (Exception e) {
            return Optional.empty();
        }
    }
    
    @Override
    public boolean existsByUsername(String username) {
        String sql = "SELECT COUNT(*) FROM users WHERE username = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, username);
        return count != null && count > 0;
    }
    
    @Override
    public boolean existsByEmail(String email) {
        String sql = "SELECT COUNT(*) FROM users WHERE email = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, email);
        return count != null && count > 0;
    }
    
    @Override
    public void delete(User user) {
        deleteById(user.getId());
    }
    
    @Override
    public void deleteById(Long id) {
        String sql = "DELETE FROM users WHERE id = ?";
        jdbcTemplate.update(sql, id);
    }
}