package com.example.usertest.repository;

import com.example.usertest.entity.User;
import java.util.Optional;

/**
 * 用户仓库接口
 * 定义了用户数据的访问方法
 */
public interface UserRepository {
    
    /**
     * 保存用户
     * @param user 用户实体
     * @return 保存后的用户（包含ID）
     */
    User save(User user);
    
    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户Optional对象
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 根据邮箱查找用户
     * @param email 邮箱
     * @return 用户Optional对象
     */
    Optional<User> findByEmail(String email);
    
    /**
     * 根据ID查找用户
     * @param id 用户ID
     * @return 用户Optional对象
     */
    Optional<User> findById(Long id);
    
    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return true如果存在，false如果不存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return true如果存在，false如果不存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 删除用户
     * @param user 用户实体
     */
    void delete(User user);
    
    /**
     * 根据ID删除用户
     * @param id 用户ID
     */
    void deleteById(Long id);
}