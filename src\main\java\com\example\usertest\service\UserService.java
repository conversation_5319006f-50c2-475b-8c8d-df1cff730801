package com.example.usertest.service;

import com.example.usertest.dto.*;
import com.example.usertest.entity.User;
import com.example.usertest.exception.*;
import com.example.usertest.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * 用户服务实现类
 * 包含用户注册、登录、密码修改、删除等业务逻辑
 */
@Service
public class UserService {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    
    // 邮箱正则表达式
    private static final Pattern EMAIL_PATTERN = 
        Pattern.compile("^[A-Za-z0-9+_.-]+@(.+)$");
    
    // 密码长度限制
    private static final int MIN_PASSWORD_LENGTH = 6;
    private static final int MAX_PASSWORD_LENGTH = 20;
    
    @Autowired
    public UserService(UserRepository userRepository, PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }
    
    /**
     * 用户注册
     * @param request 注册请求对象
     * @return 注册成功的用户DTO
     * @throws ValidationException 当输入验证失败时
     * @throws UserAlreadyExistsException 当用户名或邮箱已存在时
     */
    public UserDTO registerUser(RegisterRequest request) {
        // 1. 验证输入参数
        validateRegisterRequest(request);
        
        // 2. 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new UserAlreadyExistsException(
                "Username already exists: " + request.getUsername()
            );
        }
        
        // 3. 检查邮箱是否已存在
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new UserAlreadyExistsException(
                "Email already exists: " + request.getEmail()
            );
        }
        
        // 4. 创建新用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setActive(true);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        
        // 5. 保存用户
        User savedUser = userRepository.save(user);
        
        // 6. 转换为DTO并返回
        return convertToDTO(savedUser);
    }
    
    /**
     * 用户登录
     * @param request 登录请求对象
     * @return 登录成功的用户DTO
     * @throws ValidationException 当输入验证失败时
     * @throws UserNotFoundException 当用户不存在时
     * @throws InvalidPasswordException 当密码错误时
     */
    public UserDTO loginUser(LoginRequest request) {
        // 1. 验证输入参数
        validateLoginRequest(request);
        
        // 2. 查找用户
        User user = userRepository.findByUsername(request.getUsername())
            .orElseThrow(() -> new UserNotFoundException(
                "User not found with username: " + request.getUsername()
            ));
        
        // 3. 检查用户是否激活
        if (!user.isActive()) {
            throw new ValidationException("User account is deactivated");
        }
        
        // 4. 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            throw new InvalidPasswordException("Invalid password");
        }
        
        // 5. 更新最后登录时间
        user.setUpdatedAt(LocalDateTime.now());
        userRepository.save(user);
        
        // 6. 返回用户DTO
        return convertToDTO(user);
    }
    
    /**
     * 修改密码
     * @param userId 用户ID
     * @param request 修改密码请求对象
     * @throws UserNotFoundException 当用户不存在时
     * @throws InvalidPasswordException 当旧密码错误或新密码不符合要求时
     */
    public void changePassword(Long userId, ChangePasswordRequest request) {
        // 1. 验证输入参数
        validateChangePasswordRequest(request);
        
        // 2. 查找用户
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new UserNotFoundException(userId));
        
        // 3. 验证旧密码
        if (!passwordEncoder.matches(request.getOldPassword(), user.getPassword())) {
            throw new InvalidPasswordException("Old password is incorrect");
        }
        
        // 4. 验证新密码不能与旧密码相同
        if (request.getOldPassword().equals(request.getNewPassword())) {
            throw new ValidationException("New password must be different from old password");
        }
        
        // 5. 更新密码
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        user.setUpdatedAt(LocalDateTime.now());
        userRepository.save(user);
    }
    
    /**
     * 删除用户（软删除）
     * @param userId 用户ID
     * @throws UserNotFoundException 当用户不存在时
     */
    public void deleteUser(Long userId) {
        // 1. 验证用户ID
        if (userId == null || userId <= 0) {
            throw new ValidationException("Invalid user ID");
        }
        
        // 2. 查找用户
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new UserNotFoundException(userId));
        
        // 3. 软删除（设置为非激活状态）
        user.setActive(false);
        user.setUpdatedAt(LocalDateTime.now());
        userRepository.save(user);
    }
    
    /**
     * 根据ID获取用户
     * @param userId 用户ID
     * @return 用户DTO
     * @throws UserNotFoundException 当用户不存在时
     */
    public UserDTO getUserById(Long userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new UserNotFoundException(userId));
        return convertToDTO(user);
    }
    
    /**
     * 验证注册请求
     */
    private void validateRegisterRequest(RegisterRequest request) {
        if (request == null) {
            throw new ValidationException("Register request cannot be null");
        }
        
        // 验证用户名
        if (request.getUsername() == null || request.getUsername().trim().isEmpty()) {
            throw new ValidationException("Username cannot be empty");
        }
        if (request.getUsername().length() < 3 || request.getUsername().length() > 20) {
            throw new ValidationException("Username must be between 3 and 20 characters");
        }
        if (!request.getUsername().matches("^[a-zA-Z0-9_]+$")) {
            throw new ValidationException("Username can only contain letters, numbers and underscore");
        }
        
        // 验证密码
        validatePassword(request.getPassword());
        
        // 验证邮箱
        if (request.getEmail() == null || request.getEmail().trim().isEmpty()) {
            throw new ValidationException("Email cannot be empty");
        }
        if (!EMAIL_PATTERN.matcher(request.getEmail()).matches()) {
            throw new ValidationException("Invalid email format");
        }
    }
    
    /**
     * 验证登录请求
     */
    private void validateLoginRequest(LoginRequest request) {
        if (request == null) {
            throw new ValidationException("Login request cannot be null");
        }
        if (request.getUsername() == null || request.getUsername().trim().isEmpty()) {
            throw new ValidationException("Username cannot be empty");
        }
        if (request.getPassword() == null || request.getPassword().trim().isEmpty()) {
            throw new ValidationException("Password cannot be empty");
        }
    }
    
    /**
     * 验证修改密码请求
     */
    private void validateChangePasswordRequest(ChangePasswordRequest request) {
        if (request == null) {
            throw new ValidationException("Change password request cannot be null");
        }
        if (request.getOldPassword() == null || request.getOldPassword().trim().isEmpty()) {
            throw new ValidationException("Old password cannot be empty");
        }
        validatePassword(request.getNewPassword());
    }
    
    /**
     * 验证密码格式
     */
    private void validatePassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            throw new ValidationException("Password cannot be empty");
        }
        if (password.length() < MIN_PASSWORD_LENGTH || password.length() > MAX_PASSWORD_LENGTH) {
            throw new ValidationException(
                String.format("Password must be between %d and %d characters", 
                    MIN_PASSWORD_LENGTH, MAX_PASSWORD_LENGTH)
            );
        }
        if (!password.matches(".*[0-9].*")) {
            throw new ValidationException("Password must contain at least one digit");
        }
        if (!password.matches(".*[a-zA-Z].*")) {
            throw new ValidationException("Password must contain at least one letter");
        }
    }
    
    /**
     * 将User实体转换为UserDTO
     */
    private UserDTO convertToDTO(User user) {
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setEmail(user.getEmail());
        dto.setPhone(user.getPhone());
        dto.setActive(user.isActive());
        return dto;
    }
}