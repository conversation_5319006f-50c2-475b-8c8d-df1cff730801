package com.example.usertest.service;

import com.example.usertest.dto.*;
import com.example.usertest.entity.User;
import com.example.usertest.exception.*;
import com.example.usertest.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InOrder;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UserService单元测试类
 * 使用JUnit 5和Mockito进行测试
 * 
 * 关键知识点：
 * 1. @ExtendWith(MockitoExtension.class) - 启用Mockito支持
 * 2. @Mock - 创建模拟对象
 * 3. @InjectMocks - 注入模拟对象到被测试类
 * 4. @Nested - 组织相关测试用例
 * 5. @DisplayName - 为测试提供可读的名称
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("UserService单元测试")
class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private PasswordEncoder passwordEncoder;
    
    @InjectMocks
    private UserService userService;
    
    // 测试数据
    private User testUser;
    private RegisterRequest validRegisterRequest;
    private LoginRequest validLoginRequest;
    private ChangePasswordRequest validChangePasswordRequest;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setPassword("encodedPassword");
        testUser.setEmail("<EMAIL>");
        testUser.setActive(true);
        testUser.setCreatedAt(LocalDateTime.now());
        testUser.setUpdatedAt(LocalDateTime.now());
        
        validRegisterRequest = new RegisterRequest("newuser", "Password123", "<EMAIL>");
        validLoginRequest = new LoginRequest("testuser", "Password123");
        validChangePasswordRequest = new ChangePasswordRequest("oldPassword", "NewPassword123");
    }
    
    /**
     * 用户注册测试
     */
    @Nested
    @DisplayName("用户注册测试")
    class RegisterUserTests {
        
        @Test
        @DisplayName("成功注册新用户")
        void registerUser_Success() {
            // Given - 准备测试数据和模拟行为
            when(userRepository.existsByUsername(anyString())).thenReturn(false);
            when(userRepository.existsByEmail(anyString())).thenReturn(false);
            when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
            when(userRepository.save(any(User.class))).thenAnswer(invocation -> {
                User user = invocation.getArgument(0);
                user.setId(1L);
                return user;
            });
            
            // When - 执行测试
            UserDTO result = userService.registerUser(validRegisterRequest);
            
            // Then - 验证结果
            assertThat(result).isNotNull();
            assertThat(result.getUsername()).isEqualTo("newuser");
            assertThat(result.getEmail()).isEqualTo("<EMAIL>");
            assertThat(result.isActive()).isTrue();
            
            // 验证方法调用
            verify(userRepository).existsByUsername("newuser");
            verify(userRepository).existsByEmail("<EMAIL>");
            verify(passwordEncoder).encode("Password123");
            verify(userRepository).save(any(User.class));
            
            // 验证保存的用户对象
            ArgumentCaptor<User> userCaptor = ArgumentCaptor.forClass(User.class);
            verify(userRepository).save(userCaptor.capture());
            User savedUser = userCaptor.getValue();
            assertThat(savedUser.getUsername()).isEqualTo("newuser");
            assertThat(savedUser.getPassword()).isEqualTo("encodedPassword");
        }
        
        @Test
        @DisplayName("用户名已存在时注册失败")
        void registerUser_UsernameExists() {
            // Given
            when(userRepository.existsByUsername("newuser")).thenReturn(true);
            
            // When/Then
            assertThatThrownBy(() -> userService.registerUser(validRegisterRequest))
                .isInstanceOf(UserAlreadyExistsException.class)
                .hasMessageContaining("Username already exists: newuser");
            
            // 验证没有调用save方法
            verify(userRepository, never()).save(any(User.class));
        }
        
        @Test
        @DisplayName("邮箱已存在时注册失败")
        void registerUser_EmailExists() {
            // Given
            when(userRepository.existsByUsername(anyString())).thenReturn(false);
            when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);
            
            // When/Then
            assertThatThrownBy(() -> userService.registerUser(validRegisterRequest))
                .isInstanceOf(UserAlreadyExistsException.class)
                .hasMessageContaining("Email already exists: <EMAIL>");
        }
        
        @Test
        @DisplayName("空请求注册失败")
        void registerUser_NullRequest() {
            assertThatThrownBy(() -> userService.registerUser(null))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Register request cannot be null");
        }
        
        @Test
        @DisplayName("用户名为空时注册失败")
        void registerUser_EmptyUsername() {
            RegisterRequest request = new RegisterRequest("", "Password123", "<EMAIL>");
            
            assertThatThrownBy(() -> userService.registerUser(request))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Username cannot be empty");
        }
        
        @Test
        @DisplayName("用户名太短时注册失败")
        void registerUser_UsernameTooShort() {
            RegisterRequest request = new RegisterRequest("ab", "Password123", "<EMAIL>");
            
            assertThatThrownBy(() -> userService.registerUser(request))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Username must be between 3 and 20 characters");
        }
        
        @Test
        @DisplayName("用户名包含非法字符时注册失败")
        void registerUser_InvalidUsernameCharacters() {
            RegisterRequest request = new RegisterRequest("user@name", "Password123", "<EMAIL>");
            
            assertThatThrownBy(() -> userService.registerUser(request))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Username can only contain letters, numbers and underscore");
        }
        
        @Test
        @DisplayName("密码太短时注册失败")
        void registerUser_PasswordTooShort() {
            RegisterRequest request = new RegisterRequest("newuser", "Pass1", "<EMAIL>");
            
            assertThatThrownBy(() -> userService.registerUser(request))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Password must be between 6 and 20 characters");
        }
        
        @Test
        @DisplayName("密码没有数字时注册失败")
        void registerUser_PasswordNoDigit() {
            RegisterRequest request = new RegisterRequest("newuser", "Password", "<EMAIL>");
            
            assertThatThrownBy(() -> userService.registerUser(request))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Password must contain at least one digit");
        }
        
        @Test
        @DisplayName("密码没有字母时注册失败")
        void registerUser_PasswordNoLetter() {
            RegisterRequest request = new RegisterRequest("newuser", "123456", "<EMAIL>");
            
            assertThatThrownBy(() -> userService.registerUser(request))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Password must contain at least one letter");
        }
        
        @Test
        @DisplayName("邮箱格式无效时注册失败")
        void registerUser_InvalidEmail() {
            RegisterRequest request = new RegisterRequest("newuser", "Password123", "invalid-email");
            
            assertThatThrownBy(() -> userService.registerUser(request))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Invalid email format");
        }
    }
    
    /**
     * 用户登录测试
     */
    @Nested
    @DisplayName("用户登录测试")
    class LoginUserTests {
        
        @Test
        @DisplayName("成功登录")
        void loginUser_Success() {
            // Given
            when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches("Password123", "encodedPassword")).thenReturn(true);
            when(userRepository.save(any(User.class))).thenReturn(testUser);
            
            // When
            UserDTO result = userService.loginUser(validLoginRequest);
            
            // Then
            assertThat(result).isNotNull();
            assertThat(result.getUsername()).isEqualTo("testuser");
            assertThat(result.getEmail()).isEqualTo("<EMAIL>");
            
            // 验证更新了最后登录时间
            verify(userRepository).save(argThat(user -> 
                user.getUpdatedAt() != null && user.getUpdatedAt().isAfter(testUser.getCreatedAt())
            ));
        }
        
        @Test
        @DisplayName("用户不存在时登录失败")
        void loginUser_UserNotFound() {
            // Given
            when(userRepository.findByUsername("testuser")).thenReturn(Optional.empty());
            
            // When/Then
            assertThatThrownBy(() -> userService.loginUser(validLoginRequest))
                .isInstanceOf(UserNotFoundException.class)
                .hasMessageContaining("User not found with username: testuser");
        }
        
        @Test
        @DisplayName("用户未激活时登录失败")
        void loginUser_UserNotActive() {
            // Given
            testUser.setActive(false);
            when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
            
            // When/Then
            assertThatThrownBy(() -> userService.loginUser(validLoginRequest))
                .isInstanceOf(ValidationException.class)
                .hasMessage("User account is deactivated");
        }
        
        @Test
        @DisplayName("密码错误时登录失败")
        void loginUser_InvalidPassword() {
            // Given
            when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches("Password123", "encodedPassword")).thenReturn(false);
            
            // When/Then
            assertThatThrownBy(() -> userService.loginUser(validLoginRequest))
                .isInstanceOf(InvalidPasswordException.class)
                .hasMessage("Invalid password");
        }
        
        @Test
        @DisplayName("空请求登录失败")
        void loginUser_NullRequest() {
            assertThatThrownBy(() -> userService.loginUser(null))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Login request cannot be null");
        }
        
        @Test
        @DisplayName("用户名为空时登录失败")
        void loginUser_EmptyUsername() {
            LoginRequest request = new LoginRequest("", "password");
            
            assertThatThrownBy(() -> userService.loginUser(request))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Username cannot be empty");
        }
        
        @Test
        @DisplayName("密码为空时登录失败")
        void loginUser_EmptyPassword() {
            LoginRequest request = new LoginRequest("username", "");
            
            assertThatThrownBy(() -> userService.loginUser(request))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Password cannot be empty");
        }
    }
    
    /**
     * 修改密码测试
     */
    @Nested
    @DisplayName("修改密码测试")
    class ChangePasswordTests {
        
        @Test
        @DisplayName("成功修改密码")
        void changePassword_Success() {
            // Given
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches("oldPassword", "encodedPassword")).thenReturn(true);
            when(passwordEncoder.encode("NewPassword123")).thenReturn("newEncodedPassword");
            when(userRepository.save(any(User.class))).thenReturn(testUser);
            
            // When
            userService.changePassword(1L, validChangePasswordRequest);
            
            // Then
            ArgumentCaptor<User> userCaptor = ArgumentCaptor.forClass(User.class);
            verify(userRepository).save(userCaptor.capture());
            User savedUser = userCaptor.getValue();
            
            assertThat(savedUser.getPassword()).isEqualTo("newEncodedPassword");
            assertThat(savedUser.getUpdatedAt()).isNotNull();
            
            // 验证方法调用顺序
            verify(userRepository).findById(1L);
            verify(passwordEncoder).matches("oldPassword", "encodedPassword");
            verify(passwordEncoder).encode("NewPassword123");
        }
        
        @Test
        @DisplayName("用户不存在时修改密码失败")
        void changePassword_UserNotFound() {
            // Given
            when(userRepository.findById(1L)).thenReturn(Optional.empty());
            
            // When/Then
            assertThatThrownBy(() -> userService.changePassword(1L, validChangePasswordRequest))
                .isInstanceOf(UserNotFoundException.class)
                .hasMessage("User not found with id: 1");
        }
        
        @Test
        @DisplayName("旧密码错误时修改失败")
        void changePassword_InvalidOldPassword() {
            // Given
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches("oldPassword", "encodedPassword")).thenReturn(false);
            
            // When/Then
            assertThatThrownBy(() -> userService.changePassword(1L, validChangePasswordRequest))
                .isInstanceOf(InvalidPasswordException.class)
                .hasMessage("Old password is incorrect");
        }
        
        @Test
        @DisplayName("新旧密码相同时修改失败")
        void changePassword_SamePassword() {
            // Given
            ChangePasswordRequest request = new ChangePasswordRequest("samePassword", "samePassword");
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches("samePassword", "encodedPassword")).thenReturn(true);
            
            // When/Then
            assertThatThrownBy(() -> userService.changePassword(1L, request))
                .isInstanceOf(ValidationException.class)
                .hasMessage("New password must be different from old password");
        }
        
        @Test
        @DisplayName("空请求修改密码失败")
        void changePassword_NullRequest() {
            assertThatThrownBy(() -> userService.changePassword(1L, null))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Change password request cannot be null");
        }
        
        @Test
        @DisplayName("新密码格式无效时修改失败")
        void changePassword_InvalidNewPassword() {
            ChangePasswordRequest request = new ChangePasswordRequest("oldPassword", "short");
            
            assertThatThrownBy(() -> userService.changePassword(1L, request))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Password must be between 6 and 20 characters");
        }
    }
    
    /**
     * 删除用户测试
     */
    @Nested
    @DisplayName("删除用户测试")
    class DeleteUserTests {
        
        @Test
        @DisplayName("成功删除用户（软删除）")
        void deleteUser_Success() {
            // Given
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(userRepository.save(any(User.class))).thenReturn(testUser);
            
            // When
            userService.deleteUser(1L);
            
            // Then
            ArgumentCaptor<User> userCaptor = ArgumentCaptor.forClass(User.class);
            verify(userRepository).save(userCaptor.capture());
            User savedUser = userCaptor.getValue();
            
            assertThat(savedUser.isActive()).isFalse();
            assertThat(savedUser.getUpdatedAt()).isNotNull();
            
            // 验证是软删除而非硬删除
            verify(userRepository, never()).delete(any(User.class));
            verify(userRepository, never()).deleteById(anyLong());
        }
        
        @Test
        @DisplayName("用户不存在时删除失败")
        void deleteUser_UserNotFound() {
            // Given
            when(userRepository.findById(1L)).thenReturn(Optional.empty());
            
            // When/Then
            assertThatThrownBy(() -> userService.deleteUser(1L))
                .isInstanceOf(UserNotFoundException.class)
                .hasMessage("User not found with id: 1");
        }
        
        @Test
        @DisplayName("无效的用户ID时删除失败")
        void deleteUser_InvalidUserId() {
            assertThatThrownBy(() -> userService.deleteUser(null))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Invalid user ID");
            
            assertThatThrownBy(() -> userService.deleteUser(0L))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Invalid user ID");
            
            assertThatThrownBy(() -> userService.deleteUser(-1L))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Invalid user ID");
        }
    }
    
    /**
     * 获取用户测试
     */
    @Nested
    @DisplayName("获取用户测试")
    class GetUserTests {
        
        @Test
        @DisplayName("成功获取用户")
        void getUserById_Success() {
            // Given
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            
            // When
            UserDTO result = userService.getUserById(1L);
            
            // Then
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(1L);
            assertThat(result.getUsername()).isEqualTo("testuser");
            assertThat(result.getEmail()).isEqualTo("<EMAIL>");
            assertThat(result.isActive()).isTrue();
        }
        
        @Test
        @DisplayName("用户不存在时获取失败")
        void getUserById_NotFound() {
            // Given
            when(userRepository.findById(1L)).thenReturn(Optional.empty());
            
            // When/Then
            assertThatThrownBy(() -> userService.getUserById(1L))
                .isInstanceOf(UserNotFoundException.class)
                .hasMessage("User not found with id: 1");
        }
    }
    
    /**
     * 边界条件测试
     */
    @Nested
    @DisplayName("边界条件测试")
    class BoundaryTests {
        
        @Test
        @DisplayName("用户名长度边界测试")
        void username_BoundaryTest() {
            // 最小长度（3个字符）
            RegisterRequest minRequest = new RegisterRequest("abc", "Password123", "<EMAIL>");
            when(userRepository.existsByUsername(anyString())).thenReturn(false);
            when(userRepository.existsByEmail(anyString())).thenReturn(false);
            when(passwordEncoder.encode(anyString())).thenReturn("encoded");
            when(userRepository.save(any(User.class))).thenAnswer(i -> i.getArgument(0));
            
            assertThatNoException().isThrownBy(() -> userService.registerUser(minRequest));
            
            // 最大长度（20个字符）
            RegisterRequest maxRequest = new RegisterRequest(new String(new char[20]).replace('\0', 'a'), "Password123", "<EMAIL>");
            assertThatNoException().isThrownBy(() -> userService.registerUser(maxRequest));
            
            // 超过最大长度（21个字符）
            RegisterRequest overMaxRequest = new RegisterRequest(new String(new char[21]).replace('\0', 'a'), "Password123", "<EMAIL>");
            assertThatThrownBy(() -> userService.registerUser(overMaxRequest))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Username must be between 3 and 20 characters");
        }
        
        @Test
        @DisplayName("密码长度边界测试")
        void password_BoundaryTest() {
            // 最小长度（6个字符）
            RegisterRequest minRequest = new RegisterRequest("testuser", "Pass12", "<EMAIL>");
            when(userRepository.existsByUsername(anyString())).thenReturn(false);
            when(userRepository.existsByEmail(anyString())).thenReturn(false);
            when(passwordEncoder.encode(anyString())).thenReturn("encoded");
            when(userRepository.save(any(User.class))).thenAnswer(i -> i.getArgument(0));
            
            assertThatNoException().isThrownBy(() -> userService.registerUser(minRequest));
            
            // 最大长度（20个字符）
            RegisterRequest maxRequest = new RegisterRequest("testuser", "Pass1" + new String(new char[15]).replace('\0', 'a'), "<EMAIL>");
            assertThatNoException().isThrownBy(() -> userService.registerUser(maxRequest));
            
            // 超过最大长度（21个字符）
            RegisterRequest overMaxRequest = new RegisterRequest("testuser", "Pass1" + new String(new char[16]).replace('\0', 'a'), "<EMAIL>");
            assertThatThrownBy(() -> userService.registerUser(overMaxRequest))
                .isInstanceOf(ValidationException.class)
                .hasMessage("Password must be between 6 and 20 characters");
        }
    }
    
    /**
     * Mock验证测试
     * 演示如何验证Mock对象的交互
     */
    @Nested
    @DisplayName("Mock验证测试")
    class MockVerificationTests {
        
        @Test
        @DisplayName("验证方法调用次数")
        void verifyMethodCallCount() {
            // Given
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            
            // When
            userService.getUserById(1L);
            userService.getUserById(1L);
            
            // Then - 验证方法被调用了2次
            verify(userRepository, times(2)).findById(1L);
        }
        
        @Test
        @DisplayName("验证方法从未被调用")
        void verifyMethodNeverCalled() {
            // Given
            RegisterRequest request = new RegisterRequest("", "password", "email");
            
            // When
            assertThatThrownBy(() -> userService.registerUser(request))
                .isInstanceOf(ValidationException.class);
            
            // Then - 验证save方法从未被调用
            verify(userRepository, never()).save(any(User.class));
            verify(passwordEncoder, never()).encode(anyString());
        }
        
        @Test
        @DisplayName("验证方法调用顺序")
        void verifyMethodCallOrder() {
            // Given
            when(userRepository.existsByUsername(anyString())).thenReturn(false);
            when(userRepository.existsByEmail(anyString())).thenReturn(false);
            when(passwordEncoder.encode(anyString())).thenReturn("encoded");
            when(userRepository.save(any(User.class))).thenAnswer(i -> i.getArgument(0));
            
            // When
            userService.registerUser(validRegisterRequest);
            
            // Then - 验证方法调用顺序
            InOrder inOrder = inOrder(userRepository, passwordEncoder);
            inOrder.verify(userRepository).existsByUsername("newuser");
            inOrder.verify(userRepository).existsByEmail("<EMAIL>");
            inOrder.verify(passwordEncoder).encode("Password123");
            inOrder.verify(userRepository).save(any(User.class));
        }
    }
}