# Database Configuration
# H2 database for development/testing
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# MySQL database configuration (uncomment to use MySQL)
# spring.datasource.url=**************************************************************************************************
# spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# spring.datasource.username=root
# spring.datasource.password=yourpassword

# MyBatis-Plus Configuration
mybatis-plus.mapper-locations=classpath:mapper/**/*.xml
mybatis-plus.type-aliases-package=com.example.usertest.entity
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# Create table automatically for H2
spring.h2.console.enabled=true
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql
spring.sql.init.continue-on-error=true

# Logging
logging.level.com.example.usertest=DEBUG
logging.level.org.springframework=INFO
logging.level.com.baomidou.mybatisplus=DEBUG