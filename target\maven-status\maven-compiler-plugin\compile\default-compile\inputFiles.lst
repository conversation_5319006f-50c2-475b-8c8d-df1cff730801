E:\aug-ai\springboot-test\src\main\java\com\example\usertest\config\SecurityConfig.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\repository\JdbcUserRepository.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\repository\UserRepository.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\exception\UserNotFoundException.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\dto\ChangePasswordRequest.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\service\UserService.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\exception\InvalidPasswordException.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\exception\UserAlreadyExistsException.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\dto\LoginRequest.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\dto\UserDTO.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\dto\RegisterRequest.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\entity\User.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\repository\MyBatisPlusUserRepository.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\exception\ValidationException.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\UserTestApplication.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\mapper\UserMapper.java
E:\aug-ai\springboot-test\src\main\java\com\example\usertest\config\MyBatisPlusConfig.java
