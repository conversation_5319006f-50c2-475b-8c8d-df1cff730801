<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.example.usertest.service.UserServiceTest$GetUserTests" time="0.012" tests="2" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="E:\aug-ai\springboot-test\target\test-classes;E:\aug-ai\springboot-test\target\classes;D:\program\maven-res\org\springframework\boot\spring-boot-starter\2.7.17\spring-boot-starter-2.7.17.jar;D:\program\maven-res\org\springframework\boot\spring-boot\2.7.17\spring-boot-2.7.17.jar;D:\program\maven-res\org\springframework\spring-context\5.3.30\spring-context-5.3.30.jar;D:\program\maven-res\org\springframework\boot\spring-boot-autoconfigure\2.7.17\spring-boot-autoconfigure-2.7.17.jar;D:\program\maven-res\org\springframework\boot\spring-boot-starter-logging\2.7.17\spring-boot-starter-logging-2.7.17.jar;D:\program\maven-res\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;D:\program\maven-res\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;D:\program\maven-res\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;D:\program\maven-res\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;D:\program\maven-res\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;D:\program\maven-res\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\program\maven-res\org\springframework\spring-core\5.3.30\spring-core-5.3.30.jar;D:\program\maven-res\org\springframework\spring-jcl\5.3.30\spring-jcl-5.3.30.jar;D:\program\maven-res\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;D:\program\maven-res\org\springframework\boot\spring-boot-starter-security\2.7.17\spring-boot-starter-security-2.7.17.jar;D:\program\maven-res\org\springframework\spring-aop\5.3.30\spring-aop-5.3.30.jar;D:\program\maven-res\org\springframework\spring-beans\5.3.30\spring-beans-5.3.30.jar;D:\program\maven-res\org\springframework\security\spring-security-config\5.7.11\spring-security-config-5.7.11.jar;D:\program\maven-res\org\springframework\security\spring-security-web\5.7.11\spring-security-web-5.7.11.jar;D:\program\maven-res\org\springframework\spring-expression\5.3.30\spring-expression-5.3.30.jar;D:\program\maven-res\org\springframework\spring-web\5.3.30\spring-web-5.3.30.jar;D:\program\maven-res\com\baomidou\mybatis-plus-boot-starter\3.5.4.1\mybatis-plus-boot-starter-3.5.4.1.jar;D:\program\maven-res\com\baomidou\mybatis-plus\3.5.4.1\mybatis-plus-3.5.4.1.jar;D:\program\maven-res\com\baomidou\mybatis-plus-core\3.5.4.1\mybatis-plus-core-3.5.4.1.jar;D:\program\maven-res\com\baomidou\mybatis-plus-annotation\3.5.4.1\mybatis-plus-annotation-3.5.4.1.jar;D:\program\maven-res\com\baomidou\mybatis-plus-extension\3.5.4.1\mybatis-plus-extension-3.5.4.1.jar;D:\program\maven-res\org\mybatis\mybatis\3.5.13\mybatis-3.5.13.jar;D:\program\maven-res\com\github\jsqlparser\jsqlparser\4.6\jsqlparser-4.6.jar;D:\program\maven-res\org\mybatis\mybatis-spring\2.1.1\mybatis-spring-2.1.1.jar;D:\program\maven-res\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.4.1\mybatis-plus-spring-boot-autoconfigure-3.5.4.1.jar;D:\program\maven-res\org\springframework\boot\spring-boot-starter-jdbc\2.7.17\spring-boot-starter-jdbc-2.7.17.jar;D:\program\maven-res\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\program\maven-res\org\springframework\spring-jdbc\5.3.30\spring-jdbc-5.3.30.jar;D:\program\maven-res\org\springframework\spring-tx\5.3.30\spring-tx-5.3.30.jar;D:\program\maven-res\com\h2database\h2\2.1.214\h2-2.1.214.jar;D:\program\maven-res\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\program\maven-res\org\springframework\boot\spring-boot-starter-test\2.7.17\spring-boot-starter-test-2.7.17.jar;D:\program\maven-res\org\springframework\boot\spring-boot-test\2.7.17\spring-boot-test-2.7.17.jar;D:\program\maven-res\org\springframework\boot\spring-boot-test-autoconfigure\2.7.17\spring-boot-test-autoconfigure-2.7.17.jar;D:\program\maven-res\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;D:\program\maven-res\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;D:\program\maven-res\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;D:\program\maven-res\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\program\maven-res\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;D:\program\maven-res\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\program\maven-res\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\program\maven-res\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\program\maven-res\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\program\maven-res\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\program\maven-res\org\springframework\spring-test\5.3.30\spring-test-5.3.30.jar;D:\program\maven-res\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\program\maven-res\org\springframework\security\spring-security-test\5.7.11\spring-security-test-5.7.11.jar;D:\program\maven-res\org\springframework\security\spring-security-core\5.7.11\spring-security-core-5.7.11.jar;D:\program\maven-res\org\springframework\security\spring-security-crypto\5.7.11\spring-security-crypto-5.7.11.jar;D:\program\maven-res\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;D:\program\maven-res\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;D:\program\maven-res\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\program\maven-res\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;D:\program\maven-res\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\program\maven-res\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;D:\program\maven-res\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;D:\program\maven-res\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;D:\program\maven-res\org\mockito\mockito-core\4.5.1\mockito-core-4.5.1.jar;D:\program\maven-res\net\bytebuddy\byte-buddy\1.12.23\byte-buddy-1.12.23.jar;D:\program\maven-res\net\bytebuddy\byte-buddy-agent\1.12.23\byte-buddy-agent-1.12.23.jar;D:\program\maven-res\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\program\maven-res\org\mockito\mockito-junit-jupiter\4.5.1\mockito-junit-jupiter-4.5.1.jar;D:\program\maven-res\org\assertj\assertj-core\3.22.0\assertj-core-3.22.0.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 10"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk1.8.0_172\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire7527824369367525420\surefirebooter-20250627180951567_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire7527824369367525420 2025-06-27T18-09-50_826-jvmRun1 surefire-20250627180951567_1tmp surefire_0-20250627180951567_2tmp"/>
    <property name="surefire.test.class.path" value="E:\aug-ai\springboot-test\target\test-classes;E:\aug-ai\springboot-test\target\classes;D:\program\maven-res\org\springframework\boot\spring-boot-starter\2.7.17\spring-boot-starter-2.7.17.jar;D:\program\maven-res\org\springframework\boot\spring-boot\2.7.17\spring-boot-2.7.17.jar;D:\program\maven-res\org\springframework\spring-context\5.3.30\spring-context-5.3.30.jar;D:\program\maven-res\org\springframework\boot\spring-boot-autoconfigure\2.7.17\spring-boot-autoconfigure-2.7.17.jar;D:\program\maven-res\org\springframework\boot\spring-boot-starter-logging\2.7.17\spring-boot-starter-logging-2.7.17.jar;D:\program\maven-res\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;D:\program\maven-res\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;D:\program\maven-res\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;D:\program\maven-res\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;D:\program\maven-res\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;D:\program\maven-res\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\program\maven-res\org\springframework\spring-core\5.3.30\spring-core-5.3.30.jar;D:\program\maven-res\org\springframework\spring-jcl\5.3.30\spring-jcl-5.3.30.jar;D:\program\maven-res\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;D:\program\maven-res\org\springframework\boot\spring-boot-starter-security\2.7.17\spring-boot-starter-security-2.7.17.jar;D:\program\maven-res\org\springframework\spring-aop\5.3.30\spring-aop-5.3.30.jar;D:\program\maven-res\org\springframework\spring-beans\5.3.30\spring-beans-5.3.30.jar;D:\program\maven-res\org\springframework\security\spring-security-config\5.7.11\spring-security-config-5.7.11.jar;D:\program\maven-res\org\springframework\security\spring-security-web\5.7.11\spring-security-web-5.7.11.jar;D:\program\maven-res\org\springframework\spring-expression\5.3.30\spring-expression-5.3.30.jar;D:\program\maven-res\org\springframework\spring-web\5.3.30\spring-web-5.3.30.jar;D:\program\maven-res\com\baomidou\mybatis-plus-boot-starter\3.5.4.1\mybatis-plus-boot-starter-3.5.4.1.jar;D:\program\maven-res\com\baomidou\mybatis-plus\3.5.4.1\mybatis-plus-3.5.4.1.jar;D:\program\maven-res\com\baomidou\mybatis-plus-core\3.5.4.1\mybatis-plus-core-3.5.4.1.jar;D:\program\maven-res\com\baomidou\mybatis-plus-annotation\3.5.4.1\mybatis-plus-annotation-3.5.4.1.jar;D:\program\maven-res\com\baomidou\mybatis-plus-extension\3.5.4.1\mybatis-plus-extension-3.5.4.1.jar;D:\program\maven-res\org\mybatis\mybatis\3.5.13\mybatis-3.5.13.jar;D:\program\maven-res\com\github\jsqlparser\jsqlparser\4.6\jsqlparser-4.6.jar;D:\program\maven-res\org\mybatis\mybatis-spring\2.1.1\mybatis-spring-2.1.1.jar;D:\program\maven-res\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.4.1\mybatis-plus-spring-boot-autoconfigure-3.5.4.1.jar;D:\program\maven-res\org\springframework\boot\spring-boot-starter-jdbc\2.7.17\spring-boot-starter-jdbc-2.7.17.jar;D:\program\maven-res\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\program\maven-res\org\springframework\spring-jdbc\5.3.30\spring-jdbc-5.3.30.jar;D:\program\maven-res\org\springframework\spring-tx\5.3.30\spring-tx-5.3.30.jar;D:\program\maven-res\com\h2database\h2\2.1.214\h2-2.1.214.jar;D:\program\maven-res\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\program\maven-res\org\springframework\boot\spring-boot-starter-test\2.7.17\spring-boot-starter-test-2.7.17.jar;D:\program\maven-res\org\springframework\boot\spring-boot-test\2.7.17\spring-boot-test-2.7.17.jar;D:\program\maven-res\org\springframework\boot\spring-boot-test-autoconfigure\2.7.17\spring-boot-test-autoconfigure-2.7.17.jar;D:\program\maven-res\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;D:\program\maven-res\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;D:\program\maven-res\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;D:\program\maven-res\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\program\maven-res\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;D:\program\maven-res\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\program\maven-res\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\program\maven-res\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\program\maven-res\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\program\maven-res\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\program\maven-res\org\springframework\spring-test\5.3.30\spring-test-5.3.30.jar;D:\program\maven-res\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\program\maven-res\org\springframework\security\spring-security-test\5.7.11\spring-security-test-5.7.11.jar;D:\program\maven-res\org\springframework\security\spring-security-core\5.7.11\spring-security-core-5.7.11.jar;D:\program\maven-res\org\springframework\security\spring-security-crypto\5.7.11\spring-security-crypto-5.7.11.jar;D:\program\maven-res\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;D:\program\maven-res\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;D:\program\maven-res\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\program\maven-res\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;D:\program\maven-res\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\program\maven-res\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;D:\program\maven-res\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;D:\program\maven-res\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;D:\program\maven-res\org\mockito\mockito-core\4.5.1\mockito-core-4.5.1.jar;D:\program\maven-res\net\bytebuddy\byte-buddy\1.12.23\byte-buddy-1.12.23.jar;D:\program\maven-res\net\bytebuddy\byte-buddy-agent\1.12.23\byte-buddy-agent-1.12.23.jar;D:\program\maven-res\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\program\maven-res\org\mockito\mockito-junit-jupiter\4.5.1\mockito-junit-jupiter-4.5.1.jar;D:\program\maven-res\org\assertj\assertj-core\3.22.0\assertj-core-3.22.0.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk1.8.0_172\jre"/>
    <property name="basedir" value="E:\aug-ai\springboot-test"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire7527824369367525420\surefirebooter-20250627180951567_3.jar"/>
    <property name="sun.boot.class.path" value="C:\Program Files\Java\jdk1.8.0_172\jre\lib\resources.jar;C:\Program Files\Java\jdk1.8.0_172\jre\lib\rt.jar;C:\Program Files\Java\jdk1.8.0_172\jre\lib\sunrsasign.jar;C:\Program Files\Java\jdk1.8.0_172\jre\lib\jsse.jar;C:\Program Files\Java\jdk1.8.0_172\jre\lib\jce.jar;C:\Program Files\Java\jdk1.8.0_172\jre\lib\charsets.jar;C:\Program Files\Java\jdk1.8.0_172\jre\lib\jfr.jar;C:\Program Files\Java\jdk1.8.0_172\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_172-b11"/>
    <property name="user.name" value="omsrv"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="C:\Program Files\Java\jdk1.8.0_172\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\program\maven-res"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="1.8.0_172"/>
    <property name="user.dir" value="E:\aug-ai\springboot-test"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk1.8.0_172\jre\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Java\jdk1.8.0_172\bin;C:\Program Files\Java\jdk1.8.0_172\jre\bin;D:\program\mysql-5.7.44-winx64\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Bandizip\;C:\Program Files\dotnet\;D:\program\maven\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\program\nodejs\node_global;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\program\anaconda3;D:\program\anaconda3\Scripts;D:\program\anaconda3\Library\mingw-w64\bin;D:\program\anaconda3\Library\usr\bin;D:\program\anaconda3\Library\bin;C:\Program Files (x86)\Tencent\寰俊web寮�鍙戣�呭伐鍏穃dll;;;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\UGit\app-5.23.2\resources\app\git\cmd;C:\Users\<USER>\AppData\Local\UGit\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.172-b11"/>
    <property name="java.ext.dirs" value="C:\Program Files\Java\jdk1.8.0_172\jre\lib\ext;C:\Windows\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="getUserById_Success" classname="com.example.usertest.service.UserServiceTest$GetUserTests" time="0.007"/>
  <testcase name="getUserById_NotFound" classname="com.example.usertest.service.UserServiceTest$GetUserTests" time="0.002"/>
</testsuite>